import Combine
import CrateServices
import Factory
import Foundation
import MSAL
import SwiftData
import Swift<PERSON>

@main
struct CrateNFCApp: App {
  @StateObject private var userState = UserState.shared
  @StateObject var deepLinkHandler = DeepLinkHandler()
  private let container = Container()

  let standardURLs = [
    URL(string: "https://api.cratenfc.com")!,
    URL(string: "http://192.168.1.64:8000")!
  ]

  init() {
    if UserDefaults.standard.serverURL == nil {
      print("🚀 First launch: Setting default server URL")
      let defaultURL = standardURLs[1].absoluteString
      UserDefaults.standard.serverURL = defaultURL
    }
    print("📡 Current server URL: \(UserDefaults.standard.serverURL ?? "none")")
    MSALGlobalConfig.loggerConfig.logLevel = .nothing
    MSALGlobalConfig.loggerConfig.setLogCallback { _, message, containsPII in
      if !containsPII {
        print("MSAL: \(message ?? "")")
      }
    }
    // Instantiate the userService on App init so that the user is logged in from *previous cached login.
    _ = container.userService.resolve()

    // Clear collections and tracks on startup
    //        Task {
    //          await CrateNFCApp.clearUserData()
    //        }
  }

  // Static method to clear all user data (collections and tracks)
  private static func clearUserData() async {
    let container = Container()
    let modelContext = container.modelContext.resolve()

    do {
      // Clear Content objects first (since collections may reference content)
      let contentDescriptor = FetchDescriptor<Content>()
      let content = try modelContext.fetch(contentDescriptor)

      for contentItem in content {
        modelContext.delete(contentItem)
      }

      // Clear TrendingContent
      let trendingDescriptor = FetchDescriptor<TrendingContent>()
      let trendingContent = try modelContext.fetch(trendingDescriptor)

      for trendingItem in trendingContent {
        modelContext.delete(trendingItem)
      }

      // Clear RecentCollection
      let recentDescriptor = FetchDescriptor<RecentCollection>()
      let recentCollections = try modelContext.fetch(recentDescriptor)

      for collection in recentCollections {
        modelContext.delete(collection)
      }

      // Clear Collection
      let collectionDescriptor = FetchDescriptor<Collection>()
      let collections = try modelContext.fetch(collectionDescriptor)

      for collection in collections {
        modelContext.delete(collection)
      }

      try modelContext.save()
      print(
        "✅ Successfully cleared data: \(content.count) Content, \(trendingContent.count) TrendingContent, \(recentCollections.count) RecentCollections, and \(collections.count) Collections"
      )
    } catch {
      print("❌ Error clearing user data: \(error.localizedDescription)")
    }
  }

  var body: some Scene {
    WindowGroup {
      LaunchView()
        .environmentObject(deepLinkHandler)
        .environmentObject(userState)
        .onOpenURL { url in
          deepLinkHandler.handleURL(url)
        }
    }
  }
}

/// Dependency Injection Registry for CrateNFC App.
///
/// This extension defines all service factories for the application using Factory's DI system.
/// Each service is lazily instantiated with controlled lifetime (singleton, cached, etc.)
/// and automatically resolves its dependencies through the container graph.
///
/// Benefits:
/// - Centralizes the creation and wiring of all app dependencies.
/// - Avoids global singletons and tight coupling.
/// - Supports easy mocking and overriding in tests.
/// - Enables clean architecture and testable business logic.
///
/// Usage example:
///     let userService = Container.shared.userService.resolve()
///
/// Service lifetimes:
/// - `singleton`: shared across the entire app lifecycle (e.g. UserState, DeepLinkHandler)
/// - `cached`: reused within the current resolution chain, but can be reset (e.g. ApiService, UserService)
///
/// For more details on Factory, see: https://github.com/hmlongco/Factory

extension Container {
  var userState: Factory<UserState> {
    self { UserState.shared }.singleton
  }

  var httpClient: Factory<HTTPClient> {
    self {
      let defaultURL = "https://api.cratenfc.com"
      let serverURL = UserDefaults.standard.serverURL ?? defaultURL
      let client = HTTPClient(baseURL: serverURL)
      if let token = self.userState.resolve().token {
        Task {
          await client.setBearerToken(token)
        }
      }

      // HTTPClient automatically observes server URL changes internally
      return client
    }.singleton
  }

  var modelContext: Factory<ModelContext> {
    self {
      let config = ModelConfiguration(
        schema: Schema([
          Collection.self, User.self, RecentCollection.self, Content.self, TrendingContent.self
        ]),
        isStoredInMemoryOnly: false
      )

      do {
        let container = try ModelContainer(
          for: Collection.self, User.self, RecentCollection.self, Content.self,
          TrendingContent.self,
          configurations: config
        )
        return ModelContext(container)
      } catch {
        print("💾 SwiftData error: \(error)")

        // If migration fails due to schema changes, delete the store and recreate
        let storeURL = config.url
        print("🗑️ Attempting to delete corrupted store at: \(storeURL)")
        try? FileManager.default.removeItem(at: storeURL)

        // Try again with fresh store
        do {
          let container = try ModelContainer(
            for: Collection.self, User.self, RecentCollection.self, Content.self,
            TrendingContent.self,
            configurations: config
          )
          print("✅ Successfully created fresh ModelContainer")
          return ModelContext(container)
        } catch {
          print("💾 Still failed after deleting store: \(error)")
        }

        // Fallback to in-memory storage when persistent storage fails
        let fallbackConfig = ModelConfiguration(isStoredInMemoryOnly: true)
        do {
          let fallbackContainer = try ModelContainer(
            for: Collection.self, User.self, RecentCollection.self, Content.self,
            TrendingContent.self,
            configurations: fallbackConfig
          )
          return ModelContext(fallbackContainer)
        } catch {
          fatalError("Failed to create even in-memory ModelContainer: \(error)")
        }
      }
    }.singleton
  }

  var crateActor: Factory<CrateActor> {
    self {
      // Resolve the ModelContainer from the same factory that provides ModelContext
      let modelContainerInstance = self.modelContext().container
      return CrateActor(modelContainer: modelContainerInstance)
    }
    // Choose a scope:
    // .shared // if you want one CrateActor instance reused (careful with @ModelActor state if any besides context)
    // .cached // if different parts of the app might need their "own" instance but it can be reused in a resolution chain
    // .singleton // if you absolutely only want one CrateActor for the entire app.
    // Given @ModelActor has its own context, .cached or .shared is often fine.
    // If it's truly stateless beyond its modelContext, .singleton might even work,
    // but @ModelActors are often instantiated where needed.
    // Let's go with .cached as a reasonable default.
    .cached
  }

  var apiService: Factory<ApiService> {
    self {
      ApiService(
        client: self.httpClient.resolve(),
        userState: self.userState.resolve()
      )
    }.cached
  }

  var userService: Factory<UserServiceProtocol> {
    self {
      UserService(
        client: self.httpClient.resolve(),
        userState: self.userState.resolve()
      )
    }.cached
  }

  var registrationService: Factory<RegistrationServiceProtocol> {
    self {
      RegistrationService(
        client: self.httpClient.resolve(),
        userState: self.userState.resolve(),
        userService: self.userService.resolve()
      )
    }.cached
  }

  var passwordResetService: Factory<PasswordResetServiceProtocol> {
    self {
      PasswordResetService(
        client: self.httpClient.resolve(),
        userState: self.userState.resolve(),
        userService: self.userService.resolve()
      )
    }.cached
  }

  var collectionService: Factory<CollectionServiceProtocol> {
    self {
      CollectionService(
        client: self.httpClient.resolve(),
        userState: self.userState.resolve()
      )
    }.cached
  }

  var contentService: Factory<ContentServiceProtocol> {
    self {
      ContentService(
        client: self.httpClient.resolve(),
        userState: self.userState.resolve()
      )
    }.cached
  }

  var deepLinkHandler: Factory<DeepLinkHandler> {
    self { DeepLinkHandler() }.singleton
  }

  var unfurlService: Factory<UnfurlServiceProtocol> {
    self { UnfurlService() }.cached
  }
}

public class DeepLinkHandler: ObservableObject {
  @Published var deepLinkURL: URL?
  @Published var currentTab: NavView.Tab = .write
  func handleURL(_ url: URL) {
    if let components = URLComponents(url: url, resolvingAgainstBaseURL: false),
       let queryItems = components.queryItems,
       let urlString = queryItems.first(where: { $0.name == "url" })?.value,
       let extractedURL = URL(string: urlString) {
      deepLinkURL = extractedURL
      currentTab = .write
    } else {
      deepLinkURL = nil
    }
  }
}
